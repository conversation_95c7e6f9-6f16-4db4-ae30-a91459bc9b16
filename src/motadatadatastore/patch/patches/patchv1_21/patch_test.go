/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package patchv1_21

import (
	"github.com/stretchr/testify/assert"
	"motadatadatastore/codec"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"testing"
	"time"
)

var tokenizer = utils.Tokenizer{}

func TestMain(m *testing.M) {

	if utils.SkipBenchmarkTest() {

		return
	}

	tokenizer = utils.Tokenizer{
		Tokens: make([]string, utils.TokenizerLength),
	}

	os.RemoveAll(utils.EventDir)

	utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"patchv1_21.zip", utils.CurrentDir)

	m.Run()
}

func TestPatch(t *testing.T) {

	err := Patch(&tokenizer)

	assert.Nil(t, err)

}

func TestVerticalFormat(t *testing.T) {

	directory := utils.EventDir + utils.PathSeparator + "0"

	dirs, err := os.ReadDir(directory)

	assert.Nil(t, err)

	assert.NotNil(t, dirs)

	assert.Len(t, dirs, 2)

	assert.Equal(t, dirs[0].Name(), "1710288000")

	assert.Equal(t, dirs[1].Name(), "1710374400")

	day1Tick := codec.StringToINT(dirs[0].Name())

	day2Tick := codec.StringToINT(dirs[1].Name())

	for index, dir := range dirs {

		files, err := os.ReadDir(directory + utils.PathSeparator + dir.Name())

		assert.Nil(t, err)

		assert.NotNil(t, files)

		for _, file := range files {

			utils.Split(file.Name(), utils.SpecialSeparator, &tokenizer)

			fileTick := codec.StringToINT(tokenizer.Tokens[0])

			if index == 0 {

				assert.GreaterOrEqual(t, fileTick, day1Tick)

				assert.LessOrEqual(t, fileTick, day2Tick)

			} else {

				assert.GreaterOrEqual(t, fileTick, day2Tick)

			}

		}

	}

}

func TestHorizontalFormat(t *testing.T) {

	directory := utils.EventDir + utils.PathSeparator + "1"

	dirs, err := os.ReadDir(directory)

	assert.Nil(t, err)

	assert.NotNil(t, dirs)

	assert.Len(t, dirs, 2)

	assert.Equal(t, dirs[0].Name(), "1710288000")

	assert.Equal(t, dirs[1].Name(), "1710374400")

	day1Tick := codec.StringToINT(dirs[0].Name())

	day2Tick := codec.StringToINT(dirs[1].Name())

	for index, dir := range dirs {

		files, err := os.ReadDir(directory + utils.PathSeparator + dir.Name())

		assert.Nil(t, err)

		assert.NotNil(t, files)

		for _, file := range files {

			utils.Split(file.Name(), utils.SpecialSeparator, &tokenizer)

			fileTick := codec.StringToINT(tokenizer.Tokens[0])

			if index == 0 {

				assert.GreaterOrEqual(t, fileTick, day1Tick)

				assert.LessOrEqual(t, fileTick, day2Tick)

			} else {

				assert.GreaterOrEqual(t, fileTick, day2Tick)

			}

		}

	}

}

func TestAggregationEvents(t *testing.T) {

	directory := utils.EventDir + utils.PathSeparator + "1-aggregations"

	dirs, err := os.ReadDir(directory)

	assert.Nil(t, err)

	assert.NotNil(t, dirs)

	assert.Len(t, dirs, 2)

	assert.Equal(t, dirs[0].Name(), "1710288000")

	assert.Equal(t, dirs[1].Name(), "1710374400")

	day1Tick := codec.StringToINT(dirs[0].Name())

	day2Tick := codec.StringToINT(dirs[1].Name())

	for index, dir := range dirs {

		files, err := os.ReadDir(directory + utils.PathSeparator + dir.Name())

		assert.Nil(t, err)

		assert.NotNil(t, files)

		for _, file := range files {

			utils.Split(file.Name(), utils.SpecialSeparator, &tokenizer)

			fileTick := codec.StringToINT(tokenizer.Tokens[0])

			if index == 0 {

				assert.GreaterOrEqual(t, fileTick, day1Tick)

				assert.LessOrEqual(t, fileTick, day2Tick)

			} else {

				assert.GreaterOrEqual(t, fileTick, day2Tick)

			}

		}

	}

}

func TestPatchV1(t *testing.T) {

	os.RemoveAll(path)

	defer os.RemoveAll(path)

	assertions := assert.New(t)

	os.RemoveAll(utils.EventDir)

	os.Create(utils.EventDir)

	os.RemoveAll(tempPath)

	os.MkdirAll(tempPath, os.ModePerm)

	assertions.Error(Patch(&tokenizer))

	os.RemoveAll(utils.EventDir)

	os.RemoveAll(tempPath)

	os.MkdirAll(utils.EventDir+utils.PathSeparator+"tmp"+utils.HyphenSeparator+utils.Aggregations, os.ModePerm)

	os.Create(utils.EventDir + utils.PathSeparator + "tmp" + utils.HyphenSeparator + utils.Aggregations + utils.PathSeparator + "dummy")

	path := tempPath + utils.PathSeparator + utils.VerticalFormat

	timeStamp := time.Unix(codec.StringToINT64(tokenizer.Tokens[0]), 0).UTC()

	baseTick := codec.INT64ToStringValue(time.Date(timeStamp.Year(), timeStamp.Month(), timeStamp.Day(), 0, 0, 0, 0, time.UTC).Unix())

	os.MkdirAll(path, os.ModePerm)

	os.Create(path + utils.PathSeparator + baseTick)

	assertions.Error(Patch(&tokenizer))

	os.RemoveAll(path)

	path = tempPath + utils.PathSeparator + utils.HorizontalFormat

	os.MkdirAll(path, os.ModePerm)

	os.Create(path + utils.PathSeparator + baseTick)

	assertions.Error(Patch(&tokenizer))

	os.RemoveAll(path)

	path = tempPath + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations

	os.MkdirAll(path, os.ModePerm)

	os.Create(path + utils.PathSeparator + baseTick)

	assertions.Error(Patch(&tokenizer))

	os.RemoveAll(path)

	os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.Patch + "datastore-events")

	os.Create(utils.CurrentDir + utils.PathSeparator + utils.Patch + "datastore-events")

	assertions.Error(Patch(&tokenizer))

	os.RemoveAll(path)

	path = tempPath + utils.PathSeparator + utils.HorizontalFormat

	os.MkdirAll(path, os.ModePerm)

	timeStamp = time.Now().UTC()

	baseTick = codec.INT64ToStringValue(time.Date(timeStamp.Year(), timeStamp.Month(), timeStamp.Day(), 0, 0, 0, 0, time.UTC).Unix())

	os.Create(path + utils.PathSeparator + "dummy")

	os.MkdirAll(path+utils.PathSeparator+baseTick, os.ModePerm)

	os.Create(path + utils.PathSeparator + baseTick + utils.PathSeparator + "dummy")

	os.Create(path + utils.PathSeparator + baseTick + utils.PathSeparator + "dummy")

	assertions.Error(Patch(&tokenizer))

	os.RemoveAll(tempPath)

	os.RemoveAll(utils.EventDir)
}
