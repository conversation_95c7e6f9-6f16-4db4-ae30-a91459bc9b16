/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-07             Vedant D. Dokania     	MOTADATA-4698 Modified error message "ErrorSubscriberStartFailed"
* 2025-03-05			 Aashil Shah			Motadata-5190 Added error message "ErrorFailedMetadataFile"
* 2025-05-15			 Vedant D. Dokania		Motadata-6251 Sonar Error Fixing
* 2025-05-26  			 <PERSON><PERSON>val Bera			MOTADATA-6333 Added Discard Query Threshold Error
 */

package utils

const (

	//Error Codes & Messages

	ErrorEmptyFile = "file size can not be zero"

	ErrorMaxPoolLengthLimitReached = "failed to acquire %v pool, reason: requested size %v > %v"

	ErrorMaxPoolLeaked = "%v pool leaked"

	ErrorPoolAcquire = "failed to acquire %v pool having size %v"

	ErrorSnappyDecoder = "failed to decode snappy, reason: %v"

	ErrorZSTDDecoder = "failed to decode zstd, reason: %v"

	ErrorS2Decoder = "failed to decode S2, reason: %v"

	ErrorCreateStore = "failed to create store: %v, reason: %v"

	ErrorDatastoreInit = "shutting down datastore, reason:failed to init datastore, reason:%v"

	ErrorInvalidBufferLengthInt32Bits = "failed to read uint32/int32 values, reason: invalid buffer length"

	ErrorInvalidBufferLengthInt24Bits = "failed to read int24 values, reason: invalid buffer length"

	ErrorInvalidBufferLengthFloat64Bits = "failed to read float64 values, reason: invalid buffer length"

	ErrorInvalidBufferLengthInt64Bits = "failed to read uint64/int64 values, reason: invalid buffer length"

	ErrorInvalidBufferLengthInt40Bits = "failed to read int40 values, reason: invalid buffer length"

	ErrorInvalidBufferLengthInt48Bits = "failed to read int48values, reason: invalid buffer length"

	ErrorInvalidBufferLengthInt56Bits = "failed to read int56values, reason: invalid buffer length"

	ErrorInvalidBufferLengthInt16Bits = "failed to read int16/uint16 values, reason: invalid buffer length"

	ErrorInvalidBufferLengthFloat8Bits = "failed to read float8 values, reason: invalid buffer length"

	ErrorInvalidBufferLengthFloat16Bits = "failed to read float16 values, reason: invalid buffer length"

	ErrorInvalidDataType = "failed to prob the datatype for value %v of key:%v of store %v"

	ErrorGetKey = "failed to get the value of key %v from the store %v, reason: %v"

	ErrorResultFilter = "failed to qualify result groups"

	ErrorResultTickFilter = "failed to qualify result ticks"

	ErrorNotFound = "key %v not found for the store %v"

	ErrorUpdatePostingListIndex = "failed to update the posting list index for field/metric %v in the store %v, reason: %v"

	ErrorWriteKey = "failed to write the value of key %v in the store %v, reason: %v"

	ErrorDecodeValues = "failed to decode values of %v from the store %v, reason: %v"

	ErrorUnknownDataType = "failed to write values of field/metric %v in the store %v, reason: Unsupported datatype"

	ErrorEncodeValues = "failed to encode values of key %v for the store %v, reason: %v"

	ErrorConditionExpression = "failed to evaluate the condition expression %v, reason: %v"

	ErrorGrouping = "failed to evaluate the grouping function, reason: %v"

	ErrorSortGrouping = "failed to sort the grouping"

	ErrorConditionNotQualified = "condition not qualified"

	ErrorGroupingRequired = "failed to parse the %v result, reason : grouping is required"

	ErrorGroupingEntitiesRequired = "failed to parse the result, reason : grouping entities are required"

	ErrorPluginRequired = "failed to parse the query reason : plugin is required"

	ErrorKeyQualification = "failed to qualify keys, reason : %v"

	ErrorFilterKeyQualification = "failed to qualify filter keys, reason : %v"

	ErrorCorrupted = "corrupted record"

	ErrorCorruptedRecord = "corrupted record found for %v of store %v, total entries lost: %v"

	ErrorTooLarge = "too large"

	ErrorBlobFlush = "failed to flush blob file of the partition %v, reason: %v"

	ErrorWriteBlob = "failed to write blob of the partition %v, reason: %v"

	ErrorSyncWAL = "failed to sync wal file %v of the partition %v, reason: %v"

	ErrorDataNotFound = "no valid data found for the specified time"

	ErrorQueryQueueMaxLimitReached = "query queue max limit reached, so aborting the requested query"

	ErrorQueryCreationAgeThresholdLimitReached = "query creation age threshold limit reached, aborting the requested query"

	ErrorQueryAborted = "query aborted, reason: timed out"

	ErrorStoreClosed = "%v store is closed... please retry"

	ErrorPartitionDeleted = "%v partition was deleted by retention job... please retry"

	ErrorAcquireStore = "failed to acquire store %v"

	ErrorWriteCache = "failed to write cache entry %v, reason: %v"

	ErrorInvalidCondition = "condition length is greater than 3"

	ErrorInvalidFilter = "invalid data filter"

	ErrorInvalidSortingColumn = "sorting for string datatype not allowed"

	ErrorCreatePartition = "failed to create partition, reason: %v"

	ErrorDeletedRecord = "deleted/corrupted entry %v found in the partition %v"

	ErrorMergeNumericMapping = "failed to merge numeric mapping for the store %v, reason:%v"

	ErrorUpdateMultipartMetadata = "failed to update the multipart metadata for store %v, reason: %v"

	ErrorGetOrdinal = "err %v occurred while getting ordinals for key %v for store %v, hence skipping %v records"

	ErrorIteratorDone = "iterator-done"

	ErrorDecodeDatatype = "error %v occurred while decoding %v datatype key %v , key %v , tick %v and part %v"

	ErrorEncodeDatatype = "error %v occurred while encoding %v datatype key %v , key %v , tick %v and part %v"

	ErrorDiskIO = "error %v occurred while doing io for plugin %v , tick %v and part %v"

	ErrorDecodeMetricDatatype = "error %v occurred while decoding %v datatype key %v , metric %v"

	ErrorJobStackTrace = "!!!STACK TRACE for job!!! \n %v"

	ErrorPassoverStep1EntitiesLimitExceeded = "failed to parse the query reason : passover step1 query entities limit exceeded"

	ErrorInvalidVisualizationType = "invalid visualization type"

	ErrorPostingListLookupFailed = "posting list lookup failed"

	ErrorLocateFile = "failed to find the file %v"

	ErrorGenerateNumericMappings = "failed to generate numeric mappings for key %v, store %v, reason %v"

	ErrorDecodeKeyValues = "failed to decode key %v , plugin %v , tick %v and part %v"

	ErrorResetConfigs = "error %v occurred while resetting configs for %v"

	ErrorBackupConfigs = "failed to backup configs"

	ErrorStoreBackup = "failed to backup store: %v"

	ErrorUpdateBackUpContext = "failed to update the datastore backup context"

	ErrorSyncStore = "failed to sync store: %v reason : %v"

	ErrorInvalidDataPoint = "data-points limit exceeded"

	ErrorStoreReopen = "failed to reopen the store, store : %s , reason : %s"

	ErrorAnonymousBufferAcquire = "failed to acquire anonymous buffer, reason : %s"

	ErrorBaselineInvalidDataPoint = "Unable to detect baseline, reason: insufficient data...minimum 15 points required"

	ErrorIOURing = "unexpected error %s io_uring occurred while disk/io"

	ErrorEventFileMove = "error %v occurred while moving file %v in data writer %v"

	ErrorPprofFailed = "failed to fetch the pprof profile of %v for %v, reason : %v"

	ErrorFindTopNGroups = "failed to find top groups for %v"

	ErrorIsDeleted = "deleted/corrupted"

	ErrorSubscriberStartFailed = "failed to start subscriber for %v of type %v, reason :%v"

	ErrorLogCollectionFailed = "failed to collect logs, reason : %v"

	ErrorUpdateConfigs = "failed to update %v configs , reason : %v"

	ErrorFailedMetadataFile = "failed to create metadata file for store %v, reason: %v"

	ErrorWritingStringMetric = "error %v occurred while writing string metric %v in writer %v"

	ErrorWriteStringMapping = "failed to write the string mapping in bkp file, reason: %v"

	ErrorWriteNumericMapping = "failed to write the numeric mapping in bkp file, reason: %v"

	ErrorCreateTempMappingBackup = "failed to create temp mapping backup file store : %s , reason : %s"

	ErrorRenameTempMappingBackup = "failed to rename temp mapping backup file store : %s , reason : %s"
)
