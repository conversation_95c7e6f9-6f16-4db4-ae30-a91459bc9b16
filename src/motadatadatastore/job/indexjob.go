/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>			Motadata-5190  Migrated constants from datastore to utils to match SonarQube Standard
* 2025-04-09			 Dhaval <PERSON>ra			Motadata-4859  Refactoring "fmt.Sprintf" to update go version to 1.24
* 2025-05-05			 Swapnil <PERSON>. <PERSON>		M<PERSON>A-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-10             <PERSON><PERSON><PERSON>            MOTADATA-6174  Added Unmap function to release memory-mapped resources
* 2025-06-23             <PERSON><PERSON><PERSON>ra            MOTADATA-6642  Added comments
* 2025-06-24			 <PERSON><PERSON><PERSON><PERSON>chal		Bug fixed in index() for calculating multiple day cardinality, using map.
 */

/*
	In the case of log plugins, columns are not predefined, so we don't know which columns to put in aggregation
	and indexable, so we run index jobs in which we observe data for a specific number of records and decide which
	columns to put in indexable and aggregation.

	For logs, when a batch arrives at a horizontal writer, we send it to an indexjob, which examines the data for a
	specific batch number and places columns based on it.
*/

package job

import (
	"encoding/json"
	"fmt"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/utils"
	"os"
	"time"
)

const (
	// IndexJobCTX defines the filename for persisting index job context data
	// This file stores the current state of indexing operations across restarts
	IndexJobCTX = "index-jobs.ctx"
)

// IndexJob represents a job that analyzes log data to determine which columns
// should be indexed based on their cardinality and usage patterns. It observes
// data for a specific number of records and decides which columns to put in
// indexable and aggregation based on threshold analysis.
//
// Memory layout is optimized for 64-bit systems:
// - Pointers and slices (8 bytes each) are placed first
// - Maps and channels (8 bytes each) follow
// - Structs are placed next
// - Smaller types (bool) are placed last to minimize padding
type IndexJob struct {
	// 8-byte aligned fields (pointers, slices, maps, channels)
	tokenizer             *utils.Tokenizer  // Tokenizer for processing text data during indexing operations
	context               utils.MotadataMap // Context map storing job state and column information per plugin
	ShutdownNotifications chan bool         // Channel for receiving shutdown signals from external components

	// Struct fields (size varies, but typically larger than basic types)
	logger  utils.Logger  // Logger instance for recording job events and errors (contains 2 strings)
	encoder codec.Encoder // Encoder for data serialization and deserialization operations

	// 1-byte aligned fields (placed last to minimize struct padding)
	shutdown bool // Flag indicating whether the job should terminate
}

// NewIndexJob creates and initializes a new IndexJob instance.
//
// This constructor sets up all necessary components for the index job:
// - Creates a memory pool for efficient memory management during encoding operations
// - Initializes the encoder with the memory pool for data serialization
// - Sets up an empty context map to store job state per plugin
// - Creates a logger instance for recording job events and errors
// - Initializes a buffered shutdown notification channel
//
// Returns:
//   - *IndexJob: A fully initialized IndexJob ready to start processing
func NewIndexJob() *IndexJob {

	// Create memory pool with 2 initial buffers for encoding operations
	// Uses default blob pools for efficient memory management
	pool := utils.NewMemoryPool(2, utils.MaxPoolLength, false, utils.DefaultBlobPools)

	return &IndexJob{

		// Initialize encoder with memory pool for efficient data serialization
		encoder: codec.NewEncoder(pool),

		// Initialize empty context map to store job state per plugin
		context: utils.MotadataMap{},

		// Create logger instance for recording job events and errors
		logger: utils.NewLogger("Index Job", "job"),

		// Create buffered channel for shutdown notifications (capacity: 5)
		// Buffer allows multiple shutdown signals without blocking senders
		ShutdownNotifications: make(chan bool, 5),
	}
}

// Start initializes and starts the IndexJob in a separate goroutine.
//
// This method performs the following operations:
// 1. Initializes the tokenizer with a pre-allocated token slice for text processing
// 2. Starts a background goroutine that continuously processes index requests
// 3. Registers with the job engine shutdown mutex for coordinated shutdown
// 4. Runs the main processing loop until shutdown is requested
//
// The job will continue running until either:
// - A shutdown signal is received via ShutdownNotifications channel
// - The global shutdown flag is set
// - The job's internal shutdown flag is set
func (job *IndexJob) Start() {

	// Initialize tokenizer with pre-allocated slice for efficient text processing
	// TokenizerLength defines the maximum number of tokens that can be processed
	job.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	// Start background goroutine for continuous index processing
	go func() {

		// Register with job engine shutdown mutex for coordinated shutdown
		// This ensures all jobs complete gracefully during system shutdown
		utils.JobEngineShutdownMutex.Add(1)

		// Main processing loop - continues until shutdown is requested
		for {

			// Check for shutdown conditions:
			// - job.shutdown: Internal shutdown flag
			// - utils.GlobalShutdown: System-wide shutdown flag
			if job.shutdown || utils.GlobalShutdown {

				break
			}

			// Execute main processing logic
			job.run()

		}

		// Signal completion to job engine shutdown coordinator
		utils.JobEngineShutdownMutex.Done()

	}()
}

// run executes the main processing loop for the IndexJob.
//
// This method handles the core functionality of the index job:
// 1. Sets up panic recovery to handle unexpected errors gracefully
// 2. Loads existing job context from persistent storage
// 3. Processes incoming index requests and shutdown signals
// 4. Manages memory resources during shutdown
//
// The method uses a select statement to handle two types of events:
// - Index requests from utils.IndexJobRequests channel
// - Shutdown notifications from ShutdownNotifications channel
//
// Error handling:
// - Recovers from panics and logs them as errors
// - Logs failures during job loading
// - Ensures proper resource cleanup during shutdown
func (job *IndexJob) run() {

	// Set up panic recovery to handle unexpected errors gracefully
	// This prevents the entire job from crashing due to unhandled panics
	defer func() {

		if r := recover(); r != nil {

			// Log the panic error with context for debugging
			job.logger.Error(fmt.Sprintf("err %v occurred while processing indices", r))

		}

	}()

	// Load existing job context from persistent storage
	// This restores the job state from previous runs
	if !job.loadJobs() {

		// Log error and return if job loading fails
		// This prevents processing with incomplete or corrupted state
		job.logger.Error("failed to load indexing jobs...")

		return
	}

	// Main event processing loop
	// Continues until shutdown signal is received
	for {

		select {

		// Handle incoming index requests from the global request channel
		case request := <-utils.IndexJobRequests:

			// Process the index request to update probe records
			job.updateIndexerProbeRecords(request)

		// Handle shutdown notifications
		case <-job.ShutdownNotifications:

			// Release memory-mapped resources to prevent memory leaks
			job.encoder.MemoryPool.Unmap()

			// Log shutdown event for monitoring and debugging
			job.logger.Info("shutting down...")

			// Set internal shutdown flag to stop processing
			job.shutdown = true

			// Exit the processing loop
			return

		}

	}

}

// loadJobs loads the job context from persistent storage.
//
// This method performs the following operations:
// 1. Checks if the jobs directory exists, creates it if missing
// 2. Reads the job context file if it exists
// 3. Deserializes the JSON data into the job context map
// 4. Handles various error conditions gracefully
//
// The job context contains information about:
// - Plugin-specific indexing state
// - Column information for each plugin
// - Batch processing progress
//
// Returns:
//   - bool: true if loading succeeded or no context file exists, false on critical errors
//
// Error handling:
// - Creates missing directories with appropriate permissions (0755)
// - Ignores missing context files (normal for first run)
// - Logs and returns false for critical errors (directory creation, JSON parsing)
func (job *IndexJob) loadJobs() bool {

	// Check if the jobs directory exists
	_, err := os.Stat(utils.JobDir)

	// If directory doesn't exist, create it
	if os.IsNotExist(err) {

		// Create jobs directory with read/write/execute permissions for owner
		// and read/execute permissions for group and others (0755)
		err = os.MkdirAll(utils.JobDir, 0755)

		if err != nil {

			// Log critical error - cannot proceed without jobs directory
			job.logger.Error(fmt.Sprintf("failed to create jobs dir, reason: %v", err.Error()))

			return false

		}

		// Directory created successfully, no context file to load yet
		return true
	}

	// Attempt to read the job context file
	bytes, err := os.ReadFile(utils.JobDir + utils.PathSeparator + IndexJobCTX)

	// Log read errors (except for missing file which is normal)
	if err != nil && !os.IsNotExist(err) {

		job.logger.Error(fmt.Sprintf("failed to read the job file, reason: %v", err.Error()))
	}

	// If file exists and contains data, deserialize it
	if bytes != nil && len(bytes) > 0 {

		// Unmarshal JSON data into job context map
		err = json.Unmarshal(bytes, &job.context)

		if err != nil {

			// Log critical error - corrupted context data
			job.logger.Error(fmt.Sprintf("failed to load indexer jobs, reason %v", err.Error()))

			return false
		}
	}

	// Loading completed successfully
	return true
}

// sync persists the current job context to disk storage.
//
// This method ensures that the job state is preserved across restarts by:
// 1. Serializing the job context map to formatted JSON
// 2. Writing the JSON data to the persistent context file
// 3. Handling serialization and file write errors gracefully
//
// The context data includes:
// - Plugin-specific indexing progress
// - Column information and batch counts
// - Current processing state for each plugin
//
// File permissions:
// - Uses 0644 (read/write for owner, read-only for group/others)
// - Ensures the context file is readable by monitoring tools
//
// Error handling:
// - Logs JSON marshaling errors but continues operation
// - Logs file write errors but continues operation
// - Non-critical errors don't stop job processing
func (job *IndexJob) sync() {

	// Serialize job context to formatted JSON with indentation
	// Uses single space indentation for readability in debugging
	bytes, err := json.MarshalIndent(job.context, "", " ")

	if err != nil {

		// Log serialization error but continue operation
		// Context sync failure shouldn't stop job processing
		job.logger.Error(fmt.Sprintf("failed to sync jobs, reason: %v", err.Error()))
	}

	// Write serialized context to persistent storage
	// File permissions: 0644 (owner: rw, group: r, others: r)
	err = os.WriteFile(utils.JobDir+utils.PathSeparator+IndexJobCTX, bytes, 0644)

	if err != nil {

		// Log file write error but continue operation
		// Sync failure is logged for monitoring but isn't critical
		job.logger.Error(fmt.Sprintf("failed to sync jobs, reason: %v", err.Error()))
	}

}

// index analyzes column cardinality and determines which columns should remain indexable.
//
// This method implements the core indexing decision logic by:
// 1. Analyzing historical data across multiple days to count unique values per column
// 2. Identifying columns with high cardinality that should not be indexed
// 3. Removing posting lists for columns that exceed the cardinality threshold
// 4. Preserving default log columns that should always be indexed
// 5. Updating the global indexable columns configuration
//
// Algorithm:
// - Iterates through historical data day by day (backwards from current date)
// - For each column, counts unique values across Int64 and String data types
// - Removes columns that exceed the cardinality threshold (MaxIndexProbes * IndexerThresholdPercent / 100)
// - Ensures default log columns are always included in the final indexable set
//
// Parameters:
//   - plugin: The plugin name for which to analyze and update indexable columns
//
// Side effects:
// - Modifies global indexable columns configuration
// - Removes high-cardinality column stores from disk
// - Cleans up job context for the processed plugin
func (job *IndexJob) index(plugin string) {

	// Get current timestamp and normalize to start of day (00:00:00 UTC)
	// This ensures consistent daily boundaries for historical data analysis
	timestamp := time.Now().UTC()

	timestamp = time.Date(timestamp.Year(), timestamp.Month(), timestamp.Day(), 0, 0, 0, 0, time.UTC)

	// Pre-allocate array for store names (Int64 and String variants)
	// Each column has two store types for different data types
	stores := make([]string, 2)

	// Get column information from job context for the specified plugin
	columns := job.context.GetMapValue(plugin).GetMapValue(utils.Columns)

	// Initialize maps for tracking cardinality analysis
	records := make(map[string]struct{}) // map of unique values for single column

	// Acquire string pool from memory pool for efficient store name management
	// This prevents repeated memory allocations during store processing
	poolIndex, indexStores := job.encoder.MemoryPool.AcquireStringPool(utils.NotAvailable)

	// Ensure memory pool is released when function exits to prevent memory leaks
	defer job.encoder.MemoryPool.ReleaseStringPool(poolIndex)

	// Track current position in the indexStores array for store cleanup
	position := 0

	// Iterate through each column to analyze its cardinality across historical data
	for column := range columns {

		clear(records)

		// Reset position counter
		position = 0

		// Iterate backwards through historical days to analyze column cardinality
		// Starts from current day and goes back until no data store is found
		for {

			// Flag to track if any data stores were found for the current day
			// Used to determine when to stop iterating through historical data
			found := false

			if !datastore.IsInvalidIndexableColumn(plugin, column) {

				// Generate store names for both Int64 and String data types
				// Store naming convention: timestamp_column-plugin_storetype_datatype
				stores[0] = datastore.GetStoreName(utils.UnixToSeconds(timestamp.Unix()), column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + codec.INTToStringValue(int(codec.Int64))

				stores[1] = datastore.GetStoreName(utils.UnixToSeconds(timestamp.Unix()), column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + codec.INTToStringValue(int(codec.String))

				// Check both Int64 and String stores for this column
				for _, storeName := range stores {

					// Only process if the store exists on disk
					if datastore.IsStoreAvailable(storeName) {

						// Mark that we found data for this day
						found = true

						if store := datastore.GetStore(storeName, utils.None, false, true, job.encoder, job.tokenizer); store != nil {

							// Only collect unique values if we haven't reached the cardinality threshold
							if len(records) < (utils.MaxIndexProbes*utils.IndexerThresholdPercent)/100 {

								// Get all unique keys (values) from the current store
								keys, _ := store.GetKeys(nil, nil, false, codec.Invalid)

								// Add each unique key to the records map for cardinality counting
								for _, key := range keys {

									records[string(key)] = struct{}{}

								}
							}

							// Store the store name for potential cleanup if column gets marked for removal
							indexStores[position] = storeName

							position++

						}

					}
				}

			} else {

				// Column is marked as invalid for indexing - remove it immediately
				delete(columns, column)

				// Remove both Int64 and String stores for invalid columns
				job.removeStore(datastore.GetStoreName(utils.UnixToSeconds(timestamp.Unix()), column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + codec.INTToStringValue(int(codec.Int64)))

				job.removeStore(datastore.GetStoreName(utils.UnixToSeconds(timestamp.Unix()), column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + codec.INTToStringValue(int(codec.String)))

			}

			// Exit the daily iteration loop if no data stores were found for current day
			if !found {

				break
			}

			// Move to the previous day for continued historical analysis
			timestamp = timestamp.AddDate(0, 0, -1)

		}

		// Check if column exceeds cardinality threshold and should be removed from indexing
		if len(records) >= (utils.MaxIndexProbes*utils.IndexerThresholdPercent)/100 {

			// Remove high-cardinality column from indexable set
			// These columns contain too many unique values to be efficiently searchable
			delete(columns, column)

			// Remove all data stores associated with this high-cardinality column
			for index := 0; index < position; index++ {

				job.removeStore(indexStores[index])

			}

		}

	}

	// Ensure default log columns are always included in the indexable set
	for column := range datastore.LogDefaultColumns {

		columns[column] = utils.Empty
	}

	// Update global indexable columns configuration with analyzed results
	// This makes the indexing decisions available to the entire system
	datastore.AlterIndexableColumns(plugin, columns, utils.Add)

	// Clean up job context for this plugin - analysis is complete
	// This prevents reprocessing and frees memory
	delete(job.context, plugin)
}

// updateIndexerProbeRecords processes incoming index requests and manages probe counting.
//
// This method handles the accumulation of indexing requests for plugins and triggers
// the actual indexing analysis when sufficient data has been collected. It implements
// a batching mechanism to ensure indexing decisions are based on adequate sample sizes.
//
// Process flow:
// 1. Validates that the plugin is eligible for indexing analysis
// 2. Accumulates batch sizes and column information across multiple requests
// 3. Triggers full indexing analysis when probe threshold is reached
// 4. Persists updated context to disk for crash recovery
//
// Parameters:
//   - request: MotadataMap containing plugin name, columns, and batch size information
//
// Request structure:
//   - utils.Plugin: Plugin name for which to update probe records
//   - utils.Columns: Map of column names discovered in the data
//   - utils.BatchSize: Number of records processed in this batch
//
// Threshold behavior:
// - Accumulates batches until utils.MaxIndexProbes is reached
// - Triggers indexing analysis when threshold is exceeded
// - Ensures decisions are based on statistically significant sample sizes
func (job *IndexJob) updateIndexerProbeRecords(request utils.MotadataMap) {

	// Extract plugin name from the request
	plugin := request.GetStringValue(utils.Plugin)

	// Safety check: Skip if plugin is already configured as indexable
	// This prevents reprocessing of plugins that have completed analysis
	if datastore.IsIndexablePlugin(plugin) {

		return
	}

	// Only process requests that contain column information
	if request.Contains(utils.Columns) {

		// Check if we already have context for this plugin
		if job.context.Contains(plugin) {

			// Get existing context for incremental updates
			contexts := job.context.GetMapValue(plugin)

			// Accumulate batch size across multiple requests
			// This tracks the total number of records analyzed for this plugin
			contexts[utils.BatchSize] = contexts.GetIntValue(utils.BatchSize) + request.GetIntValue(utils.BatchSize)

			// Get existing column set for this plugin
			columns := contexts.GetMapValue(utils.Columns)

			// Merge new columns with existing ones
			// This handles cases where new columns appear in later batches
			for column := range utils.ToMap(request[utils.Columns]) {

				columns[column] = utils.Empty

			}

			// Check if we've collected enough data for analysis
			// MaxIndexProbes defines the minimum sample size for reliable decisions
			if contexts.GetIntValue(utils.BatchSize) >= utils.MaxIndexProbes {

				// Trigger full indexing analysis for this plugin
				job.index(plugin)

			}

		} else {

			// First request for this plugin - initialize context
			job.context[plugin] = request
		}

		// Persist updated context to disk for crash recovery
		// This ensures progress is not lost if the system restarts
		job.sync()

	}
}

// removeStore safely removes a data store from the system.
//
// This method performs a controlled shutdown and removal of a data store by:
// 1. Verifying the store exists before attempting removal
// 2. Obtaining a store instance for proper cleanup
// 3. Closing the store with proper resource cleanup
// 4. Removing the store from the global store registry
//
// The removal process ensures:
// - No data corruption during store shutdown
// - Proper release of file handles and memory mappings
// - Clean removal from the datastore management system
//
// Parameters:
//   - storeName: The unique identifier of the store to remove
//
// Safety measures:
// - Checks store availability before attempting access
// - Verifies store closure before registry removal
// - Uses read-only access to prevent accidental data modification
//
// Error handling:
// - Silently handles missing stores (already removed or never existed)
// - Ensures partial cleanup doesn't leave system in inconsistent state
func (job *IndexJob) removeStore(storeName string) {

	// Check if the store exists before attempting removal
	// This prevents errors when trying to remove already-deleted stores
	if datastore.IsStoreAvailable(storeName) {

		// Get store instance for proper cleanup
		// Parameters: storeName, accessType=None, create=false, readOnly=true
		// Read-only access prevents accidental data modification during cleanup
		if store := datastore.GetStore(storeName, utils.None, false, true, job.encoder, job.tokenizer); store != nil {

			// Close the store with proper resource cleanup
			// This ensures file handles, memory mappings, and buffers are released
			store.Close(job.encoder)

			// Verify the store is properly closed before removing from registry
			// This prevents removal of stores that failed to close properly
			if store.IsClosed() {

				// Remove store from global datastore registry
				// This completes the cleanup process and frees system resources
				datastore.RemoveStore(storeName)
			}
		}
	}

}
